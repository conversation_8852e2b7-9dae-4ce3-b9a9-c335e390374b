var builder = DistributedApplication.CreateBuilder(args);

// Adicionar a API backend - deixar o Aspire gerir as portas automaticamente
var api = builder.AddProject<Projects.ContactsApi>("contactsapi");

// Adicionar a aplicação React frontend
builder.AddNpmApp("contacts-frontend", "../contacts-frontend")
    .WithReference(api)
    .WithEnvironment("BROWSER", "none") // Disable opening browser on npm start
    .WithHttpEndpoint(env: "PORT");

builder.Build().Run();