export interface User {
  id: number;
  username: string;
  role: 'admin' | 'user';
  createdAt: string;
}

export interface Contact {
  id: number;
  userId: number;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  createdAt: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  username: string;
  role: string;
  expiresAt: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
  role: 'admin' | 'user';
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  userId?: number;
  username?: string;
  role?: string;
}

export interface CreateContactRequest {
  userId: number;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface UpdateContactRequest {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
} 