import axios from 'axios';
import { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  RegisterResponse, 
  Contact, 
  CreateContactRequest, 
  UpdateContactRequest 
} from '../types/api';

// Usar proxy em vez de URL directa
const API_BASE_URL = '/api';

// Criar instância do axios com configuração base
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar o token JWT automaticamente
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Interceptor para lidar com respostas de erro
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expirado ou inválido - limpar storage e redirecionar para login
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authService = {
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await api.post<LoginResponse>('/auth/login', credentials);
    return response.data;
  },

  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    const response = await api.post<RegisterResponse>('/auth/register', userData);
    return response.data;
  },

  logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  isAuthenticated(): boolean {
    return !!localStorage.getItem('token');
  },

  getUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  saveAuthData(loginResponse: LoginResponse) {
    localStorage.setItem('token', loginResponse.token);
    localStorage.setItem('user', JSON.stringify({
      username: loginResponse.username,
      role: loginResponse.role,
    }));
  },
};

export const contactsService = {
  async getContacts(): Promise<Contact[]> {
    const response = await api.get<Contact[]>('/contacts');
    return response.data;
  },

  async getContact(id: number): Promise<Contact> {
    const response = await api.get<Contact>(`/contacts/${id}`);
    return response.data;
  },

  async createContact(contact: CreateContactRequest): Promise<Contact> {
    const response = await api.post<Contact>('/contacts', contact);
    return response.data;
  },

  async updateContact(id: number, contact: UpdateContactRequest): Promise<Contact> {
    const response = await api.put<Contact>(`/contacts/${id}`, contact);
    return response.data;
  },

  async deleteContact(id: number): Promise<void> {
    await api.delete(`/contacts/${id}`);
  },
}; 