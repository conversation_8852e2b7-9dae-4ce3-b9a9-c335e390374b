using ContactsApi.Models;
using Npgsql;

namespace ContactsApi.Services
{
    public class ContactService
    {
        private readonly NpgsqlDataSource _dataSource;

        public ContactService(NpgsqlDataSource dataSource)
        {
            _dataSource = dataSource;
        }

        // Get all contacts (for Admin) or user's contacts (for normal user)
        public async Task<List<Contact>> GetContactsAsync(int? userId = null)
        {
            var contacts = new List<Contact>();
            
            await using var connection = await _dataSource.OpenConnectionAsync();
            
            var query = userId.HasValue 
                ? "SELECT id, user_id, name, email, phone, address, created_at FROM contacts WHERE user_id = @userId"
                : "SELECT id, user_id, name, email, phone, address, created_at FROM contacts";
                
            await using var command = new NpgsqlCommand(query, connection);
            
            if (userId.HasValue)
                command.Parameters.AddWithValue("@userId", userId.Value);
                
            await using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                contacts.Add(new Contact
                {
                    Id = reader.GetInt32(0),                    // id
                    UserId = reader.GetInt32(1),                // user_id
                    Name = reader.GetString(2),                 // name
                    Email = reader.IsDBNull(3) ? null : reader.GetString(3),     // email
                    Phone = reader.IsDBNull(4) ? null : reader.GetString(4),     // phone
                    Address = reader.IsDBNull(5) ? null : reader.GetString(5),   // address
                    CreatedAt = reader.GetDateTime(6)           // created_at
                });
            }
            
            return contacts;
        }

        // Get contact by ID
        public async Task<Contact?> GetContactByIdAsync(int id, int? userId = null)
        {
            await using var connection = await _dataSource.OpenConnectionAsync();
            
            var query = userId.HasValue
                ? "SELECT id, user_id, name, email, phone, address, created_at FROM contacts WHERE id = @id AND user_id = @userId"
                : "SELECT id, user_id, name, email, phone, address, created_at FROM contacts WHERE id = @id";
                
            await using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            if (userId.HasValue)
                command.Parameters.AddWithValue("@userId", userId.Value);
                
            await using var reader = await command.ExecuteReaderAsync();
            
            if (await reader.ReadAsync())
            {
                return new Contact
                {
                    Id = reader.GetInt32(0),                    // id
                    UserId = reader.GetInt32(1),                // user_id
                    Name = reader.GetString(2),                 // name
                    Email = reader.IsDBNull(3) ? null : reader.GetString(3),     // email
                    Phone = reader.IsDBNull(4) ? null : reader.GetString(4),     // phone
                    Address = reader.IsDBNull(5) ? null : reader.GetString(5),   // address
                    CreatedAt = reader.GetDateTime(6)           // created_at
                };
            }
            
            return null;
        }

        // Create new contact (Admin only)
        public async Task<Contact> CreateContactAsync(Contact contact)
        {
            await using var connection = await _dataSource.OpenConnectionAsync();
            
            var query = @"
                INSERT INTO contacts (user_id, name, email, phone, address, created_at) 
                VALUES (@userId, @name, @email, @phone, @address, @createdAt)
                RETURNING id";
                
            await using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddWithValue("@userId", contact.UserId);
            command.Parameters.AddWithValue("@name", contact.Name);
            command.Parameters.AddWithValue("@email", (object?)contact.Email ?? DBNull.Value);
            command.Parameters.AddWithValue("@phone", (object?)contact.Phone ?? DBNull.Value);
            command.Parameters.AddWithValue("@address", (object?)contact.Address ?? DBNull.Value);
            command.Parameters.AddWithValue("@createdAt", contact.CreatedAt);
            
            var id = await command.ExecuteScalarAsync();
            contact.Id = (int)id!;
            
            return contact;
        }

        // Update contact (Admin only)
        public async Task<bool> UpdateContactAsync(Contact contact)
        {
            await using var connection = await _dataSource.OpenConnectionAsync();
            
            var query = @"
                UPDATE contacts 
                SET name = @name, email = @email, phone = @phone, address = @address
                WHERE id = @id";
                
            await using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", contact.Id);
            command.Parameters.AddWithValue("@name", contact.Name);
            command.Parameters.AddWithValue("@email", (object?)contact.Email ?? DBNull.Value);
            command.Parameters.AddWithValue("@phone", (object?)contact.Phone ?? DBNull.Value);
            command.Parameters.AddWithValue("@address", (object?)contact.Address ?? DBNull.Value);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        // Delete contact (Admin only)
        public async Task<bool> DeleteContactAsync(int id)
        {
            await using var connection = await _dataSource.OpenConnectionAsync();
            
            var query = "DELETE FROM contacts WHERE id = @id";
            await using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
    }
} 