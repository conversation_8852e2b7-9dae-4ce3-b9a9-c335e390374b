const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Debug: listar todas as variáveis de ambiente relacionadas com serviços
  console.log('🔍 DEBUG - Todas as variáveis de ambiente:');
  Object.keys(process.env)
    .filter(key => key.toLowerCase().includes('contactsapi') || key.toLowerCase().includes('services'))
    .forEach(key => console.log(`  ${key}: ${process.env[key]}`));
  
  // TEMPORÁRIO: Usar directamente a API que funciona
  // TODO: Quando o Aspire fornecer as variáveis correctas, usar a primeira linha
  const apiUrl = 'http://localhost:5073'; // API que funciona
  
  console.log('🔧 Proxy configurado para API:', apiUrl);
  
  app.use(
    '/api',
    createProxyMiddleware({
      target: apiUrl,
      changeOrigin: true,
      secure: false,
      logLevel: 'debug',
      onError: (err, req, res) => {
        console.error('❌ Erro no proxy:', err.message);
      },
      onProxyReq: (proxyReq, req, res) => {
        console.log('🚀 Proxy request:', req.method, req.url, '→', apiUrl + req.url);
      }
    })
  );
}; 