//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute("4aab98fd-e7b5-45ea-b184-1893cf2d92bc")]
[assembly: System.Reflection.AssemblyMetadata("dcpclipath", ("C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\8.2.2\\tools\\d" +
    "cp.exe"))]
[assembly: System.Reflection.AssemblyMetadata("dcpextensionpaths", ("C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\8.2.2\\tools\\e" +
    "xt\\"))]
[assembly: System.Reflection.AssemblyMetadata("dcpbinpath", ("C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\8.2.2\\tools\\e" +
    "xt\\bin\\"))]
[assembly: System.Reflection.AssemblyMetadata("apphostprojectpath", "C:\\Users\\<USER>\\Desktop\\Estágio\\dotNet Aspire\\AppHost\\AppHost.AppHost")]
[assembly: System.Reflection.AssemblyMetadata("aspiredashboardpath", ("C:\\Users\\<USER>\\.nuget\\packages\\aspire.dashboard.sdk.win-x64\\8.2.2\\tools\\Aspire.Da" +
    "shboard.exe"))]
[assembly: System.Reflection.AssemblyCompanyAttribute("AppHost.AppHost")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0")]
[assembly: System.Reflection.AssemblyProductAttribute("AppHost.AppHost")]
[assembly: System.Reflection.AssemblyTitleAttribute("AppHost.AppHost")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]

// Generated by the MSBuild WriteCodeFragment class.

