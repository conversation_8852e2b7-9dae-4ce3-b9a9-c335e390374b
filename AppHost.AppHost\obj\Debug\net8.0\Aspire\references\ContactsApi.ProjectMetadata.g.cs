// <auto-generated/>

namespace Projects;

[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class ContactsApi : global::Aspire.Hosting.IProjectMetadata
{
    public string ProjectPath => """C:\Users\<USER>\Desktop\Estágio\dotNet Aspire\AppHost\ContactsApi\ContactsApi.csproj""";
}
