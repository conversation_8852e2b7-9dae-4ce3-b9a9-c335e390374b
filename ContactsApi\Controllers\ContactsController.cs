using ContactsApi.Models;
using ContactsApi.Services;
using Microsoft.AspNetCore.Mvc;

namespace ContactsApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ContactsController : ControllerBase
    {
        private readonly ContactService _contactService;

        public ContactsController(ContactService contactService)
        {
            _contactService = contactService;
        }

        // GET: api/contacts
        [HttpGet]
        public async Task<ActionResult<List<Contact>>> GetContacts()
        {
            try
            {
                // Por agora, mostrar todos os contactos (depois implementamos autenticação)
                var contacts = await _contactService.GetContactsAsync();
                return Ok(contacts);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao obter contactos: {ex.Message}");
            }
        }

        // GET: api/contacts/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Contact>> GetContact(int id)
        {
            try
            {
                var contact = await _contactService.GetContactByIdAsync(id);
                
                if (contact == null)
                    return NotFound($"Contacto com ID {id} não encontrado");
                    
                return Ok(contact);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao obter contacto: {ex.Message}");
            }
        }

        // POST: api/contacts
        [HttpPost]
        public async Task<ActionResult<Contact>> CreateContact(CreateContactRequest request)
        {
            try
            {
                var contact = new Contact
                {
                    UserId = request.UserId,
                    Name = request.Name,
                    Email = request.Email,
                    Phone = request.Phone,
                    Address = request.Address,
                    CreatedAt = DateTime.UtcNow
                };

                var createdContact = await _contactService.CreateContactAsync(contact);
                return CreatedAtAction(nameof(GetContact), new { id = createdContact.Id }, createdContact);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao criar contacto: {ex.Message}");
            }
        }

        // PUT: api/contacts/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateContact(int id, UpdateContactRequest request)
        {
            try
            {
                var existingContact = await _contactService.GetContactByIdAsync(id);
                if (existingContact == null)
                    return NotFound($"Contacto com ID {id} não encontrado");

                existingContact.Name = request.Name;
                existingContact.Email = request.Email;
                existingContact.Phone = request.Phone;
                existingContact.Address = request.Address;

                var updated = await _contactService.UpdateContactAsync(existingContact);
                
                if (updated)
                    return Ok(existingContact);
                else
                    return StatusCode(500, "Erro ao actualizar contacto");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao actualizar contacto: {ex.Message}");
            }
        }

        // DELETE: api/contacts/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteContact(int id)
        {
            try
            {
                var deleted = await _contactService.DeleteContactAsync(id);
                
                if (deleted)
                    return Ok($"Contacto com ID {id} eliminado com sucesso");
                else
                    return NotFound($"Contacto com ID {id} não encontrado");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao eliminar contacto: {ex.Message}");
            }
        }
    }

    // DTOs simples para requests
    public class CreateContactRequest
    {
        public int UserId { get; set; }
        public required string Name { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Address { get; set; }
    }

    public class UpdateContactRequest
    {
        public required string Name { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Address { get; set; }
    }
} 