using ContactsApi.Models;
using ContactsApi.Services;
using Microsoft.AspNetCore.Mvc;

namespace ContactsApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly AuthService _authService;

    public AuthController(AuthService authService)
    {
        _authService = authService;
    }

    /// <summary>
    /// Efetuar login e obter token JWT
    /// </summary>
    /// <param name="request">Dad<PERSON> de login (username e password)</param>
    /// <returns>Token JWT e informações do utilizador</returns>
    [HttpPost("login")]
    public async Task<ActionResult<LoginResponse>> Login([FromBody] LoginRequest request)
    {
        if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
        {
            return BadRequest(new { message = "Username e password são obrigatórios" });
        }

        var response = await _authService.LoginAsync(request);
        
        if (response == null)
        {
            return Unauthorized(new { message = "Credenciais inválidas" });
        }

        return Ok(response);
    }

    /// <summary>
    /// Registar um novo utilizador
    /// </summary>
    /// <param name="request">Dados de registo (username, password, role)</param>
    /// <returns>Resultado do registo</returns>
    [HttpPost("register")]
    public async Task<ActionResult<RegisterResponse>> Register([FromBody] RegisterRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var response = await _authService.RegisterAsync(request);

        if (!response.Success)
        {
            return BadRequest(response);
        }

        return Ok(response);
    }

    /// <summary>
    /// Verificar se o token atual é válido
    /// </summary>
    /// <returns>Informações do utilizador autenticado</returns>
    [HttpGet("me")]
    public ActionResult GetCurrentUser()
    {
        // Este endpoint será protegido depois de configurarmos a autenticação
        return Ok(new { message = "Endpoint protegido - implementar após configuração JWT" });
    }
} 