{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Estágio\\dotNet Aspire\\AppHost\\AppHost.ServiceDefaults\\AppHost.ServiceDefaults.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Estágio\\dotNet Aspire\\AppHost\\AppHost.ServiceDefaults\\AppHost.ServiceDefaults.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Estágio\\dotNet Aspire\\AppHost\\AppHost.ServiceDefaults\\AppHost.ServiceDefaults.csproj", "projectName": "AppHost.ServiceDefaults", "projectPath": "C:\\Users\\<USER>\\Desktop\\Estágio\\dotNet Aspire\\AppHost\\AppHost.ServiceDefaults\\AppHost.ServiceDefaults.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Estágio\\dotNet Aspire\\AppHost\\AppHost.ServiceDefaults\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Http.Resilience": {"target": "Package", "version": "[8.10.0, )"}, "Microsoft.Extensions.ServiceDiscovery": {"target": "Package", "version": "[8.2.2, )"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol": {"target": "Package", "version": "[1.9.0, )"}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.9.0, )"}, "OpenTelemetry.Instrumentation.AspNetCore": {"target": "Package", "version": "[1.9.0, )"}, "OpenTelemetry.Instrumentation.Http": {"target": "Package", "version": "[1.9.0, )"}, "OpenTelemetry.Instrumentation.Runtime": {"target": "Package", "version": "[1.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}