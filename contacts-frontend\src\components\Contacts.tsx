import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService, contactsService } from '../services/api';
import { Contact } from '../types/api';

export const Contacts: React.FC = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
  });
  const navigate = useNavigate();
  const user = authService.getUser();

  useEffect(() => {
    if (!authService.isAuthenticated()) {
      navigate('/login');
      return;
    }
    loadContacts();
  }, [navigate]);

  const loadContacts = async () => {
    try {
      setLoading(true);
      const data = await contactsService.getContacts();
      setContacts(data);
    } catch (err: any) {
      setError('Erro ao carregar contactos');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    authService.logout();
    navigate('/login');
  };

  const openModal = (contact?: Contact) => {
    if (contact) {
      setEditingContact(contact);
      setFormData({
        name: contact.name,
        email: contact.email || '',
        phone: contact.phone || '',
        address: contact.address || '',
      });
    } else {
      setEditingContact(null);
      setFormData({ name: '', email: '', phone: '', address: '' });
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingContact(null);
    setFormData({ name: '', email: '', phone: '', address: '' });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingContact) {
        await contactsService.updateContact(editingContact.id, formData);
      } else {
        await contactsService.createContact({
          userId: 1, // Por agora usamos userId fixo
          ...formData,
        });
      }
      await loadContacts();
      closeModal();
    } catch (err: any) {
      setError('Erro ao guardar contacto');
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Tem a certeza que quer eliminar este contacto?')) {
      try {
        await contactsService.deleteContact(id);
        await loadContacts();
      } catch (err: any) {
        setError('Erro ao eliminar contacto');
      }
    }
  };

  const canEdit = user?.role === 'admin';

  if (loading) {
    return (
      <div style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div style={{ fontSize: '18px' }}>Carregando contactos...</div>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh' }}>
      {/* Header */}
      <div className="header">
        <div className="container">
          <div className="flex justify-between align-center">
            <div>
              <h1 style={{ fontSize: '28px', fontWeight: 'bold', margin: '0 0 4px 0' }}>Contactos</h1>
              <p style={{ color: '#6b7280', fontSize: '14px', margin: 0 }}>
                Bem-vindo, {user?.username} ({user?.role})
              </p>
            </div>
            <div className="flex space-x-4">
              {canEdit && (
                <button
                  onClick={() => openModal()}
                  className="btn btn-primary"
                >
                  Adicionar Contacto
                </button>
              )}
              <button
                onClick={handleLogout}
                className="btn btn-danger"
              >
                Sair
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container" style={{ paddingTop: '24px' }}>
        {error && (
          <div className="error mb-4">{error}</div>
        )}

        {/* Contacts Grid */}
        <div className="grid grid-cols-3">
          {contacts.map((contact) => (
            <div key={contact.id} className="card">
              <h3 style={{ fontSize: '18px', fontWeight: '500', marginBottom: '8px' }}>
                {contact.name}
              </h3>
              {contact.email && (
                <p style={{ fontSize: '14px', color: '#6b7280', margin: '4px 0' }}>
                  📧 {contact.email}
                </p>
              )}
              {contact.phone && (
                <p style={{ fontSize: '14px', color: '#6b7280', margin: '4px 0' }}>
                  📞 {contact.phone}
                </p>
              )}
              {contact.address && (
                <p style={{ fontSize: '14px', color: '#6b7280', margin: '4px 0' }}>
                  📍 {contact.address}
                </p>
              )}
              <p style={{ fontSize: '12px', color: '#9ca3af', marginTop: '8px' }}>
                Criado: {new Date(contact.createdAt).toLocaleDateString('pt-PT')}
              </p>
              
              {canEdit && (
                <div style={{ marginTop: '16px', display: 'flex', gap: '8px' }}>
                  <button
                    onClick={() => openModal(contact)}
                    style={{ 
                      background: 'none', 
                      border: 'none', 
                      color: '#4f46e5', 
                      cursor: 'pointer',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}
                  >
                    Editar
                  </button>
                  <button
                    onClick={() => handleDelete(contact.id)}
                    style={{ 
                      background: 'none', 
                      border: 'none', 
                      color: '#dc2626', 
                      cursor: 'pointer',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}
                  >
                    Eliminar
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>

        {contacts.length === 0 && (
          <div className="text-center" style={{ padding: '48px 0' }}>
            <p style={{ color: '#6b7280', fontSize: '18px', marginBottom: '16px' }}>
              Nenhum contacto encontrado
            </p>
            {canEdit && (
              <button
                onClick={() => openModal()}
                className="btn btn-primary"
              >
                Adicionar primeiro contacto
              </button>
            )}
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="modal-backdrop">
          <div className="modal">
            <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
              {editingContact ? 'Editar Contacto' : 'Adicionar Contacto'}
            </h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label className="form-label">Nome *</label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label className="form-label">Email</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label className="form-label">Telefone</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label className="form-label">Morada</label>
                <textarea
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                  rows={3}
                  className="form-input"
                  style={{ resize: 'vertical' }}
                />
              </div>
              <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px', marginTop: '16px' }}>
                <button
                  type="button"
                  onClick={closeModal}
                  className="btn btn-secondary"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                >
                  {editingContact ? 'Actualizar' : 'Criar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}; 