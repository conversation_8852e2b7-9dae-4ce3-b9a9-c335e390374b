using System.ComponentModel.DataAnnotations;

namespace ContactsApi.Models;

public class RegisterRequest
{
    [Required(ErrorMessage = "Username é obrigatório")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "Username deve ter entre 3 e 50 caracteres")]
    public string Username { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password é obrigatória")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "Password deve ter pelo menos 6 caracteres")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "Role é obrigatório")]
    [RegularExpression("^(admin|user)$", ErrorMessage = "Role deve ser 'admin' ou 'user'")]
    public string Role { get; set; } = string.Empty;
} 