// <auto-generated/>

namespace Projects;

[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class AppHost_AppHost
{
    private AppHost_AppHost() { }
    public static string ProjectPath => """C:\Users\<USER>\Desktop\Estágio\dotNet Aspire\AppHost\AppHost.AppHost""";
}
