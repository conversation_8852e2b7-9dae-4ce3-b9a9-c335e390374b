using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using BCrypt.Net;
using ContactsApi.Models;
using Microsoft.IdentityModel.Tokens;
using Npgsql;

namespace ContactsApi.Services;

public class AuthService
{
    private readonly string _connectionString;
    private readonly IConfiguration _configuration;

    public AuthService(IConfiguration configuration)
    {
        _configuration = configuration;
        _connectionString = _configuration.GetConnectionString("DefaultConnection")!;
    }

    public async Task<LoginResponse?> LoginAsync(LoginRequest request)
    {
        try
        {
            // Buscar utilizador na base de dados
            var user = await GetUserByUsernameAsync(request.Username);
            if (user == null)
            {
                return null; // Utilizador não encontrado
            }

            // Verificar password
            if (!BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
            {
                return null; // Password incorreta
            }

            // Gerar token JWT
            var token = GenerateJwtToken(user);
            var expiresAt = DateTime.UtcNow.AddHours(1);

            return new LoginResponse
            {
                Token = token,
                Username = user.Username,
                Role = user.Role,
                ExpiresAt = expiresAt
            };
        }
        catch (Exception ex)
        {
            // Log do erro (pode adicionar logging aqui)
            Console.WriteLine($"Erro no login: {ex.Message}");
            return null;
        }
    }

    private async Task<User?> GetUserByUsernameAsync(string username)
    {
        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync();

        var query = "SELECT id, username, password_hash, role, created_at FROM users WHERE username = $1";
        using var command = new NpgsqlCommand(query, connection);
        command.Parameters.AddWithValue(username);

        using var reader = await command.ExecuteReaderAsync();
        if (await reader.ReadAsync())
        {
            return new User
            {
                Id = reader.GetInt32(0),           // id
                Username = reader.GetString(1),   // username
                PasswordHash = reader.GetString(2), // password_hash
                Role = reader.GetString(3),       // role
                CreatedAt = reader.GetDateTime(4) // created_at
            };
        }

        return null;
    }

    private string GenerateJwtToken(User user)
    {
        var jwtSettings = _configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["SecretKey"] ?? "MinhaChaveSecretaSuperSeguraParaJWT2024!";
        var issuer = jwtSettings["Issuer"] ?? "ContactsApi";
        var audience = jwtSettings["Audience"] ?? "ContactsApiUsers";

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Name, user.Username),
            new Claim(ClaimTypes.Role, user.Role),
            new Claim("userId", user.Id.ToString()),
            new Claim("username", user.Username),
            new Claim("role", user.Role)
        };

        var token = new JwtSecurityToken(
            issuer: issuer,
            audience: audience,
            claims: claims,
            expires: DateTime.UtcNow.AddHours(1),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public async Task<RegisterResponse> RegisterAsync(RegisterRequest request)
    {
        try
        {
            // Verificar se utilizador já existe
            var existingUser = await GetUserByUsernameAsync(request.Username);
            if (existingUser != null)
            {
                return new RegisterResponse
                {
                    Success = false,
                    Message = "Utilizador já existe"
                };
            }

            // Hash da password
            var passwordHash = BCrypt.Net.BCrypt.HashPassword(request.Password, 11);

            // Inserir novo utilizador na base de dados
            var userId = await CreateUserAsync(request.Username, passwordHash, request.Role);

            return new RegisterResponse
            {
                Success = true,
                Message = "Utilizador criado com sucesso",
                UserId = userId,
                Username = request.Username,
                Role = request.Role
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erro no registo: {ex.Message}");
            return new RegisterResponse
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    private async Task<int> CreateUserAsync(string username, string passwordHash, string role)
    {
        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync();

        var query = "INSERT INTO users (username, password_hash, role, created_at) VALUES ($1, $2, $3, CURRENT_TIMESTAMP) RETURNING id";
        using var command = new NpgsqlCommand(query, connection);
        command.Parameters.AddWithValue(username);
        command.Parameters.AddWithValue(passwordHash);
        command.Parameters.AddWithValue(role);

        var result = await command.ExecuteScalarAsync();
        return Convert.ToInt32(result);
    }
} 